"use client"

import React, { useState, useRef, useMemo } from "react"
import MyLeads from "./my-leads"
import FindLeads from "./find-leads"
import SavedSearch from "./saved-search"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { But<PERSON> } from "@ui/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { EnvelopeIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon } from "@ui/components/icons/FontAwesomeRegular"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@ui/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Input } from "@ui/components/ui/input"
import { Textarea } from "@ui/components/ui/textarea"
import { Label } from "@ui/components/ui/label"
import { addLeadToDatabase, addLeadToSegment, addLeadToWorkflow, sendEmailToLead } from "@ui/api/leads"
import { getDatabases } from "@ui/api/database"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { DatabaseSelect } from "@ui/components/workspace/main/common/databaseSelect"

interface PeopleProps {
    activeSubTab: 'my-leads' | 'find-leads' | 'saved-search'
    onLeadCreated?: (lead: any) => void
}

// Shared Components
export const ActionButton = ({ 
    icon: Icon, 
    children, 
    onClick 
}: { 
    icon: React.ComponentType<{ className?: string }>; 
    children: React.ReactNode; 
    onClick: () => void; 
}) => (
    <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center"
    >
        <Icon className="size-3" />
        <span className="truncate">{children}</span>
    </Button>
)

export const ViewLinksModal = ({ 
    trigger,
    links 
}: { 
    trigger: React.ReactNode;
    links: Array<{ id: string; title: string; url: string }> 
}) => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
                {trigger}
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4" align="end">
                <h4 className="font-semibold text-sm mb-2">Social Links</h4>
                <div className="space-y-2">
                    {links.length === 0 ? (
                        <div className="text-xs text-muted-foreground">No links available</div>
                    ) : (
                        links.map((link) => (
                            <a
                                key={link.id}
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors p-2 rounded hover:bg-blue-50"
                            >
                                <UpRightFromSquareIcon className="size-3" />
                                <span className="truncate">{link.title}</span>
                            </a>
                        ))
                    )}
                </div>
            </PopoverContent>
        </Popover>
    )
}

interface LeadActionsDropdownProps {
    trigger: React.ReactNode
    onSendEmail?: () => void
    onAddToSegments?: () => void
    onAddToDatabase?: () => void
    onAddToWorkflow?: () => void
}

export const LeadActionsDropdown = ({ 
    trigger, 
    onSendEmail, 
    onAddToSegments, 
    onAddToDatabase, 
    onAddToWorkflow 
}: LeadActionsDropdownProps) => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {trigger}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuGroup>
                    <DropdownMenuItem onClick={onSendEmail} className="cursor-pointer">
                        <EnvelopeIcon className="mr-2 h-4 w-4" />
                        <span>Send Email</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onAddToSegments} className="cursor-pointer">
                        <ChartLineIcon className="mr-2 h-4 w-4" />
                        <span>Add to Segments</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onAddToDatabase} className="cursor-pointer">
                        <DatabaseIcon className="mr-2 h-4 w-4" />
                        <span>Add to Database</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onAddToWorkflow} className="cursor-pointer">
                        <CodeMergeIcon className="mr-2 h-4 w-4" />
                        <span>Add to Workflow</span>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

// Centralized hook for all lead-related actions, state, and utilities
export const useLeadManagement = () => {
    const { toast } = useAlert()
    const { token } = useAuth()
    const { workspace } = useWorkspace()
    const router = useRouter()
    const params = useParams()
    
    // Database-related state
    const [addToDatabaseDialogOpen, setAddToDatabaseDialogOpen] = useState(false)
    const [availableDatabases, setAvailableDatabases] = useState<any[]>([])
    const [selectedDatabaseId, setSelectedDatabaseId] = useState('')
    const [addingToDatabase, setAddingToDatabase] = useState(false)
    const [loadingDatabases, setLoadingDatabases] = useState(false)
    const [selectedLeadIdForAction, setSelectedLeadIdForAction] = useState('')
    const [dialogKey, setDialogKey] = useState(0)
    
    // Send Email state
    const [sendEmailDialogOpen, setSendEmailDialogOpen] = useState(false)
    const [emailSubject, setEmailSubject] = useState('')
    const [emailBody, setEmailBody] = useState('')
    const [sendingEmail, setSendingEmail] = useState(false)
    const [selectedLeadForEmail, setSelectedLeadForEmail] = useState<any>(null)

    // Email/Phone unlock modals
    const [emailModalOpen, setEmailModalOpen] = useState(false)
    const [phoneModalOpen, setPhoneModalOpen] = useState(false)
    const [selectedLeadId, setSelectedLeadId] = useState('')
    const unlockEmailCallbackRef = useRef<(() => void) | null>(null)
    const unlockPhoneCallbackRef = useRef<(() => void) | null>(null)

    // Lead selection state
    const [selectedLeads, setSelectedLeads] = useState<string[]>([])

    const handleAddToDatabase = async (leadId: string) => {
        console.log("🔍 handleAddToDatabase called with leadId:", leadId)
        setSelectedLeadIdForAction(leadId)
        
        // First, fetch databases
        setLoadingDatabases(true)
        
        try {
            console.log("🔍 Fetching databases...")
            const response = await getDatabases(token?.token || '', workspace?.workspace?.id || '')
            console.log("🔍 Databases response:", response)
            
            if (response.error) {
                console.error("🔍 API Error:", response.error)
                toast.error(`Failed to fetch databases: ${typeof response.error === 'string' ? response.error : 'Unknown error'}`)
                return
            }
            
            const databases = response.data?.data?.databases || []
            console.log("🔍 Setting databases:", databases)
            console.log("🔍 Database count:", databases.length)
            
            // Set all states and force re-render
            setAvailableDatabases(databases)
            setAddToDatabaseDialogOpen(true)
            setDialogKey(prev => prev + 1) // Force dialog re-render
            
            console.log("🔍 States set - databases:", databases.length, "dialog: true, key:", dialogKey + 1)
            
        } catch (error) {
            console.error("Failed to fetch databases:", error)
            toast.error("Failed to fetch databases")
        } finally {
            setLoadingDatabases(false)
        }
    }

    const handleConfirmAddToDatabase = async () => {
        if (!selectedLeadIdForAction || !selectedDatabaseId) return
        
        setAddingToDatabase(true)
        try {
            await addLeadToDatabase(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, { targetDatabaseId: selectedDatabaseId })
            toast.success("Lead added to database successfully!")
            setAddToDatabaseDialogOpen(false)
            setSelectedDatabaseId('')
            setSelectedLeadIdForAction('')
        } catch (error) {
            console.error("Failed to add lead to database:", error)
            toast.error("Failed to add lead to database")
        } finally {
            setAddingToDatabase(false)
        }
    }

    const handleSendEmail = async (leadId: string, leadData?: any) => {
        // Use provided lead data or create a basic object
        const lead = leadData || { id: leadId, name: 'Unknown Lead' }
        setSelectedLeadForEmail(lead)
        setEmailSubject('')
        setEmailBody('')
        setSendEmailDialogOpen(true)
    }

    const handleConfirmSendEmail = async () => {
        if (!selectedLeadForEmail || !emailSubject.trim() || !emailBody.trim()) {
            toast.error("Please fill in both subject and body")
            return
        }

        setSendingEmail(true)
        try {
            await sendEmailToLead(token?.token || '', workspace?.workspace?.id || '', selectedLeadForEmail.id, {
                subject: emailSubject.trim(),
                body: emailBody.trim()
            })
            toast.success("Email sent successfully!")
            setSendEmailDialogOpen(false)
            setEmailSubject('')
            setEmailBody('')
            setSelectedLeadForEmail(null)
        } catch (error) {
            console.error("Failed to send email:", error)
            toast.error("Failed to send email")
        } finally {
            setSendingEmail(false)
        }
    }

    const handleAddToSegment = async (leadId: string) => {
        try {
            await addLeadToSegment(token?.token || '', workspace?.workspace?.id || '', leadId, { name: "default-segment" })
            toast.success("Lead added to segment successfully!")
        } catch (error) {
            console.error("Failed to add to segment:", error)
            toast.error("Failed to add to segment")
        }
    }

    const handleAddToWorkflow = async (leadId: string) => {
        try {
            await addLeadToWorkflow(token?.token || '', workspace?.workspace?.id || '', leadId, { workflowId: "default-workflow" })
            toast.success("Lead added to workflow successfully!")
        } catch (error) {
            console.error("Failed to add to workflow:", error)
            toast.error("Failed to add to workflow")
        }
    }

    const handleUnlockEmail = async (leadId: string) => {
        setSelectedLeadId(leadId)
        setEmailModalOpen(true)
        if (unlockEmailCallbackRef.current) {
            unlockEmailCallbackRef.current()
        }
    }

    const handleUnlockPhone = async (leadId: string) => {
        setSelectedLeadId(leadId)
        setPhoneModalOpen(true)
        if (unlockPhoneCallbackRef.current) {
            unlockPhoneCallbackRef.current()
        }
    }

    // Filter leads function
    const getFilteredLeads = (leads: any[], searchQuery: string, filter: any) => {
        let filtered = leads

        // Apply search query
        if (searchQuery) {
            filtered = filtered.filter(lead => 
                lead.normalizedData?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                lead.normalizedData?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                lead.normalizedData?.company?.toLowerCase().includes(searchQuery.toLowerCase())
            )
        }

        // Apply filter conditions (if any filters are set)
        if (filter?.conditions?.length > 0) {
            filtered = filtered.filter(lead => {
                return filter.conditions.every((condition: any) => {
                    const value = condition.value?.toString().toLowerCase() || ''
                    const leadValue = lead[condition.columnId as keyof any]?.toString().toLowerCase() || ''
                    return leadValue.includes(value)
                })
            })
        }

        return filtered
    }

    // Get contact links for a lead
    const getContactLinks = (lead: any) => {
        const links = []
        
        if (lead.normalizedData?.linkedinUrl) {
            links.push({
                id: 'linkedin',
                title: 'LinkedIn',
                url: lead.normalizedData.linkedinUrl
            })
        }
        
        if (lead.normalizedData?.email && lead.normalizedData?.isEmailVisible) {
            links.push({
                id: 'email',
                title: 'Email',
                url: `mailto:${lead.normalizedData.email}`
            })
        }
        
        if (lead.normalizedData?.phone && lead.normalizedData?.isPhoneVisible) {
            links.push({
                id: 'phone',
                title: 'Phone',
                url: `tel:${lead.normalizedData.phone}`
            })
        }
        
        return links
    }

    // Create lead actions object
    const leadActions = {
        handleSendEmail,
        handleAddToSegment,
        handleAddToDatabase,
        handleAddToWorkflow,
        handleUnlockEmail,
        handleUnlockPhone
    }



    return {
        // State
        addToDatabaseDialogOpen,
        setAddToDatabaseDialogOpen,
        availableDatabases,
        setAvailableDatabases,
        selectedDatabaseId,
        setSelectedDatabaseId,
        addingToDatabase,
        setAddingToDatabase,
        loadingDatabases,
        setLoadingDatabases,
        selectedLeadIdForAction,
        setSelectedLeadIdForAction,
        dialogKey,
        setDialogKey,
        emailModalOpen,
        setEmailModalOpen,
        phoneModalOpen,
        setPhoneModalOpen,
        selectedLeadId,
        setSelectedLeadId,
        unlockEmailCallbackRef,
        unlockPhoneCallbackRef,
        selectedLeads,
        setSelectedLeads,
        // Actions
        leadActions,
        // Components
        SharedModals,
        // Handlers
        handleAddToDatabase,
        handleConfirmAddToDatabase,
        handleSendEmail,
        handleConfirmSendEmail,
        handleAddToSegment,
        // Email dialog state
        sendEmailDialogOpen,
        setSendEmailDialogOpen,
        emailSubject,
        setEmailSubject,
        emailBody,
        setEmailBody,
        sendingEmail,
        setSendingEmail,
        selectedLeadForEmail,
        setSelectedLeadForEmail,
        handleAddToWorkflow,
        handleUnlockEmail,
        handleUnlockPhone,
        // Utilities
        getFilteredLeads,
        getContactLinks
    }
    }

// Add to Database Popover Component
const AddToDatabasePopover = ({ 
    open, 
    onOpenChange, 
    availableDatabases, 
    selectedDatabaseId, 
    setSelectedDatabaseId, 
    addingToDatabase, 
    loadingDatabases, 
    handleConfirmAddToDatabase 
}: {
    open: boolean
    onOpenChange: (open: boolean) => void
    availableDatabases: any[]
    selectedDatabaseId: string | null
    setSelectedDatabaseId: (id: string | null) => void
    addingToDatabase: boolean
    loadingDatabases: boolean
    handleConfirmAddToDatabase: () => void
}) => {
        
        return (
        <Dialog open={open} onOpenChange={onOpenChange}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Add Lead to Database</DialogTitle>
                    <DialogDescription>
                        Select a database to add the lead to.
                    </DialogDescription>
                    </DialogHeader>
                <div className="py-4">
                    {loadingDatabases ? (
                        <div className="flex items-center justify-center py-4">
                            <div className="text-sm text-muted-foreground">Loading databases...</div>
                        </div>
                    ) : availableDatabases.length === 0 ? (
                        <div className="text-center py-4">
                            <div className="text-sm text-muted-foreground mb-2">No compatible databases found</div>
                            <div className="text-xs text-muted-foreground">
                                This lead doesn't match the field requirements of any available databases.
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div>
                            <label className="text-sm font-medium">Select Database:</label>
                                <DatabaseSelect
                                    selectedId={selectedDatabaseId}
                                    onChange={setSelectedDatabaseId}
                                    disabled={loadingDatabases}
                                    className="mt-1"
                                />
                            </div>
                        </div>
                    )}
                </div>
                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={addingToDatabase}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmAddToDatabase}
                        disabled={!selectedDatabaseId || addingToDatabase || loadingDatabases}
                        className="gap-2"
                    >
                        {addingToDatabase ? (
                            <>
                                <div className="size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                Adding...
                            </>
                        ) : (
                            <>
                                <DatabaseIcon className="size-4" />
                                Add to Database
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
        )
    }

// Send Email Dialog Component
const SendEmailDialog = ({
    open,
    onOpenChange,
    selectedLeadForEmail,
    emailSubject,
    setEmailSubject,
    emailBody,
    setEmailBody,
    sendingEmail,
    handleConfirmSendEmail
}: {
    open: boolean
    onOpenChange: (open: boolean) => void
    selectedLeadForEmail: any
    emailSubject: string
    setEmailSubject: (subject: string) => void
    emailBody: string
    setEmailBody: (body: string) => void
    sendingEmail: boolean
    handleConfirmSendEmail: () => void
}) => {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>Send Email to Lead</DialogTitle>
                </DialogHeader>
                <div className="py-4 space-y-4">
                    {selectedLeadForEmail && (
                        <div className="p-3 bg-gray-50 rounded-md">
                            <p className="text-sm text-gray-600">
                                <span className="font-medium">To:</span> {selectedLeadForEmail.name}
                            </p>
                        </div>
                    )}
                    
                    <div className="space-y-2">
                        <Label htmlFor="email-subject">Subject</Label>
                        <Input
                            id="email-subject"
                            value={emailSubject}
                            onChange={(e) => setEmailSubject(e.target.value)}
                            placeholder="Enter email subject"
                            disabled={sendingEmail}
                        />
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="email-body">Message</Label>
                        <Textarea
                            id="email-body"
                            value={emailBody}
                            onChange={(e) => setEmailBody(e.target.value)}
                            placeholder="Enter your message here..."
                            rows={8}
                            disabled={sendingEmail}
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={sendingEmail}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmSendEmail}
                        disabled={!emailSubject.trim() || !emailBody.trim() || sendingEmail}
                    >
                        {sendingEmail ? "Sending..." : "Send Email"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
        )
    }

// Shared Modals Component
const SharedModals = ({ 
    emailModalOpen,
    setEmailModalOpen,
    phoneModalOpen,
    setPhoneModalOpen,
    selectedLeadId,
    unlockEmailCallbackRef,
    unlockPhoneCallbackRef,
    addToDatabaseDialogOpen,
    setAddToDatabaseDialogOpen,
    availableDatabases,
    selectedDatabaseId,
    setSelectedDatabaseId,
    addingToDatabase,
    loadingDatabases,
    handleConfirmAddToDatabase,
    sendEmailDialogOpen,
    setSendEmailDialogOpen,
    selectedLeadForEmail,
    emailSubject,
    setEmailSubject,
    emailBody,
    setEmailBody,
    sendingEmail,
    handleConfirmSendEmail
}: {
    emailModalOpen: boolean
    setEmailModalOpen: (open: boolean) => void
    phoneModalOpen: boolean
    setPhoneModalOpen: (open: boolean) => void
    selectedLeadId: string | null
    unlockEmailCallbackRef: React.MutableRefObject<(() => void) | null>
    unlockPhoneCallbackRef: React.MutableRefObject<(() => void) | null>
    addToDatabaseDialogOpen: boolean
    setAddToDatabaseDialogOpen: (open: boolean) => void
    availableDatabases: any[]
    selectedDatabaseId: string | null
    setSelectedDatabaseId: (id: string | null) => void
    addingToDatabase: boolean
    loadingDatabases: boolean
    handleConfirmAddToDatabase: () => void
    sendEmailDialogOpen: boolean
    setSendEmailDialogOpen: (open: boolean) => void
    selectedLeadForEmail: any
    emailSubject: string
    setEmailSubject: (subject: string) => void
    emailBody: string
    setEmailBody: (body: string) => void
    sendingEmail: boolean
    handleConfirmSendEmail: () => void
}) => (
        <>
            <UnlockEmailModal
                open={emailModalOpen}
                onOpenChange={setEmailModalOpen}
                leadId={selectedLeadId}
            onUnlockSuccess={() => {
                unlockEmailCallbackRef.current?.()
                }}
            />
            <UnlockPhoneModal
                open={phoneModalOpen}
                onOpenChange={setPhoneModalOpen}
                leadId={selectedLeadId}
            onUnlockSuccess={() => {
                unlockPhoneCallbackRef.current?.()
            }}
        />
        <AddToDatabasePopover
            open={addToDatabaseDialogOpen}
            onOpenChange={setAddToDatabaseDialogOpen}
            availableDatabases={availableDatabases}
            selectedDatabaseId={selectedDatabaseId}
            setSelectedDatabaseId={setSelectedDatabaseId}
            addingToDatabase={addingToDatabase}
            loadingDatabases={loadingDatabases}
            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
        />
        <SendEmailDialog
            open={sendEmailDialogOpen}
            onOpenChange={setSendEmailDialogOpen}
            selectedLeadForEmail={selectedLeadForEmail}
            emailSubject={emailSubject}
            setEmailSubject={setEmailSubject}
            emailBody={emailBody}
            setEmailBody={setEmailBody}
            sendingEmail={sendingEmail}
            handleConfirmSendEmail={handleConfirmSendEmail}
        />
    </>
)

// Main People Component
const People = ({ activeSubTab, onLeadCreated }: PeopleProps) => {
    const leadManagement = useLeadManagement()
    const { 
        leadActions, 
        // Modal state
        emailModalOpen,
        setEmailModalOpen,
        phoneModalOpen,
        setPhoneModalOpen,
        selectedLeadId,
        unlockEmailCallbackRef,
        unlockPhoneCallbackRef,
        // Database state
        addToDatabaseDialogOpen,
        setAddToDatabaseDialogOpen,
        availableDatabases,
        selectedDatabaseId,
        setSelectedDatabaseId,
        addingToDatabase,
        loadingDatabases,
        handleConfirmAddToDatabase,
        // Email dialog state
        sendEmailDialogOpen,
        setSendEmailDialogOpen,
        emailSubject,
        setEmailSubject,
        emailBody,
        setEmailBody,
        sendingEmail,
        selectedLeadForEmail,
        handleConfirmSendEmail
    } = leadManagement
    const { token } = useAuth()
    const { workspace } = useWorkspace()

    const sharedProps = {
        leadActions,
        onLeadCreated,
        token: token?.token,
        workspaceId: workspace?.workspace?.id,
        ActionButton,
        ViewLinksModal,
        LeadActionsDropdown,
        leadManagement
    }

    const renderContent = () => {
        switch (activeSubTab) {
            case 'my-leads':
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            availableDatabases={availableDatabases}
                            selectedDatabaseId={selectedDatabaseId}
                            setSelectedDatabaseId={setSelectedDatabaseId}
                            addingToDatabase={addingToDatabase}
                            loadingDatabases={loadingDatabases}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                        />
                    </>
                )
            case 'find-leads':
                return (
                    <>
                        <FindLeads {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            availableDatabases={availableDatabases}
                            selectedDatabaseId={selectedDatabaseId}
                            setSelectedDatabaseId={setSelectedDatabaseId}
                            addingToDatabase={addingToDatabase}
                            loadingDatabases={loadingDatabases}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                        />
                    </>
                )
            case 'saved-search':
                return (
                    <>
                        <SavedSearch {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            availableDatabases={availableDatabases}
                            selectedDatabaseId={selectedDatabaseId}
                            setSelectedDatabaseId={setSelectedDatabaseId}
                            addingToDatabase={addingToDatabase}
                            loadingDatabases={loadingDatabases}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                        />
                    </>
                )
            default:
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <SharedModals 
                            emailModalOpen={emailModalOpen}
                            setEmailModalOpen={setEmailModalOpen}
                            phoneModalOpen={phoneModalOpen}
                            setPhoneModalOpen={setPhoneModalOpen}
                            selectedLeadId={selectedLeadId}
                            unlockEmailCallbackRef={unlockEmailCallbackRef}
                            unlockPhoneCallbackRef={unlockPhoneCallbackRef}
                            addToDatabaseDialogOpen={addToDatabaseDialogOpen}
                            setAddToDatabaseDialogOpen={setAddToDatabaseDialogOpen}
                            availableDatabases={availableDatabases}
                            selectedDatabaseId={selectedDatabaseId}
                            setSelectedDatabaseId={setSelectedDatabaseId}
                            addingToDatabase={addingToDatabase}
                            loadingDatabases={loadingDatabases}
                            handleConfirmAddToDatabase={handleConfirmAddToDatabase}
                            sendEmailDialogOpen={sendEmailDialogOpen}
                            setSendEmailDialogOpen={setSendEmailDialogOpen}
                            selectedLeadForEmail={selectedLeadForEmail}
                            emailSubject={emailSubject}
                            setEmailSubject={setEmailSubject}
                            emailBody={emailBody}
                            setEmailBody={setEmailBody}
                            sendingEmail={sendingEmail}
                            handleConfirmSendEmail={handleConfirmSendEmail}
                        />
                    </>
                )
        }
    }

    return (
        <>
            {renderContent()}
        </>
    )
}

export default People
