import {TypedUpstreamApiResponse, UpstreamApiResponse} from "@ui/utils/http";
import axios, {AxiosError, Method} from "axios";
import {strReplaceAll} from "@repo/app-db-utils/src";

export const apiUrl = () => {
    // Provide fallback to prevent undefined errors, but actual functionality will be blocked at component level
    return process.env.NEXT_PUBLIC_API_URL || 'https://api-not-configured.local';
}

export const publicApiUrl = () => strReplaceAll('/api/v1', apiUrl() || '', '')

export const googleRecaptchaSiteKey = () => {
    return process.env.NEXT_PUBLIC_RECAPTCHA_SITEKEY;
}

export const collabServiceUrl = () => {
    return process.env.NEXT_PUBLIC_COLLAB_SERVICE_URL;
}

export const collabServiceHash = () => {
    return process.env.NEXT_PUBLIC_COLLAB_SERVICE_HASH;
}

export const defaultAPIMessage = () => {
    return 'Error occurred while making request'
}

export interface APIResponseData<T> {
    data: T
    message: string
    status: string
}

export interface DataLoad<T> {
    error?: string
    isLoading?: boolean
    isReady?: boolean
    canRetry?: boolean
    canLoad?: boolean
    hasMore?: boolean
    page?: number
    data?: T | null
    load?: () => void
    loadMore?: () => void
    refresh?: () => void
    loadedAt?: number
}

export type ItemDataLoad<T> = Pick<DataLoad<T>, "error" | "isLoading" | "data"> & {
    canRetry?: boolean
}

export type ProgressiveItemDataLoad<T> = ItemDataLoad<T> & {
    progress?: number
}

export type BackendAPIResponse<T> = TypedUpstreamApiResponse<APIResponseData<T>>

export const normalizeResponse = <T>(response: TypedUpstreamApiResponse<any>): BackendAPIResponse<T> => {
    if (!response.data || response.data.status !== 'ok') {
        response.error = response.data?.error || response.data?.message || defaultAPIMessage()
    }
    return response
}

export interface UploadCallback {
    onStart: () => void
    onProgress: (percent: number) => void
    onComplete: (response: UpstreamApiResponse) => void
}

export const UploadFile = (method: Method, url: string, token: string, fileKey: string, file: File, callback: UploadCallback, body?: object) => {
    const data = new FormData();
    data.append(fileKey, file, file.name);

    if (body) {
        for (let key of Object.keys(body)) {
            data.append(key, body[key as keyof typeof body])
        }
    }


    const onProgress = (event: any) => {
        const percent = Math.round((100 * event.loaded) / event.total)
        callback.onProgress(percent)
    }
    const headers = {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${token}`,
    }
    callback.onStart()
    axios({
        method,
        url,
        data,
        headers,
        onUploadProgress: onProgress
    }).then((raw) => {
        const isSuccess = true
        const res: UpstreamApiResponse = {
            status: raw ? raw.status : 0,
            data: raw ? raw.data : undefined,
            isSuccess,
        }
        callback.onComplete(res)
    }).catch((e) => {

        const ex = e as AxiosError
        const raw = ex.response
        const error = ex.message
        const exception = ex

        const res: UpstreamApiResponse = {
            status: raw ? raw.status : 0,
            data: raw ? raw.data : undefined,
            isSuccess: false,
            exception,
            error
        }
        callback.onComplete(res)
    })
}

